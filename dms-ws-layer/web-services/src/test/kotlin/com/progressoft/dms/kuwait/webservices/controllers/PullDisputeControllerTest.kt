package com.progressoft.dms.kuwait.webservices.controllers

import com.fasterxml.jackson.databind.ObjectMapper
import com.progressoft.dms.kuwait.webservices.config.TestConfig
import com.progressoft.dms.kuwait.webservices.controllers.TestUtil.Companion.fromJSON
import com.progressoft.dms.kuwait.webservices.dtos.PullDisputesDto
import com.progressoft.dms.kuwait.webservices.errorhandling.ResponseBuilderDto
import org.hamcrest.CoreMatchers.hasItems
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@WebMvcTest(PullDisputesController::class)
@ContextConfiguration(classes = [TestConfig::class])
@ActiveProfiles("test")
class PullDisputeControllerTest {

    @Autowired
    private lateinit var mvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Test
    fun givenValidRequest_whenCallPullDisputesApi_thenShouldReturn200Response() {
        val request = PullDisputesDto(
            messageId = "12345",
            messageDate = "2025-03-02T22:00:04",
            bankCode = "QMPC"
        )
        val size = 10
        val page = 0
        mvc.perform(
            post(END_POINT)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .param("size", size.toString())
                .param("page", page.toString()))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.message").value(SUCCESS))
            .andExpect(jsonPath("$.data").exists())
    }

    @Test
    fun givenMissingFields_whenCallPullDisputesApi_thenShouldReturn400Response() {
        val invalidRequest = PullDisputesDto(
            messageId = "",
            messageDate = "2025-03-02T22:00",
            bankCode = ""
        )
        val size = 10
        val page = 0
        val apiResponse = mvc.perform(
            post(END_POINT)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest))
                .param("size", size.toString())
                .param("page", page.toString())
        )
            .andExpect(status().isBadRequest)
            .andExpect(jsonPath("$.message").value(ERROR))
            .andExpect(jsonPath("$.errorList").isArray)
            .andReturn().response.contentAsString
        val response = fromJSON(apiResponse, ResponseBuilderDto::class.java)
        val errorListMessage = (response.errorList?.map { it.message }?.toMutableList())
        assertThat(
            errorListMessage, hasItems(
                "Message date must follow the pattern yyyy-MM-dd'T'HH:mm:ss",
                "Message date must be exactly 19 characters long",
                "Bank Code cannot be blank",
                "Message ID cannot be blank",

            )
        )
    }

    companion object {
        private const val SUCCESS = "success"
        private const val ERROR = "error"
        private const val END_POINT = "/api/v1/pull"
    }
}